import { classes } from "@/utils/classes";
import type {
  ButtonHTMLAttributes,
  DetailedHTMLProps,
  PropsWithChildren,
} from "react";

export type HtmlButtonProps = DetailedHTMLProps<
  ButtonHTMLAttributes<HTMLButtonElement>,
  HTMLButtonElement
>;

export type ButtonProps = {
  size: "small" | "normal" | "large";
  hasCustomColor?: boolean;
} & HtmlButtonProps;

export const Button: React.FC<PropsWithChildren<ButtonProps>> = ({
  children,
  size,
  hasCustomColor,
  className,
  ...others
}) => {
  return (
    <button
      {...others}
      type="button"
      className={classes(
        "rounded font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ",
        !hasCustomColor
          ? "bg-indigo-600 hover:bg-indigo-500 focus-visible:outline-indigo-600"
          : undefined,
        size === "small" ? "px-2 py-1 text-xs" : undefined,
        size === "normal" ? "px-2 py-1 text-sm" : undefined,
        size === "large" ? "px-2.5 py-1.5 text-sm" : undefined,
        className,
      )}
    >
      {children}
    </button>
  );
};
