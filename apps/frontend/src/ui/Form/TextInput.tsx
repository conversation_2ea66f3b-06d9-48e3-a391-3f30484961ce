import { type FieldHookConfig, useField } from "formik";

type Props = {
  config: string | FieldHookConfig<unknown>;
};

export const TextInput: React.FC<Props> = ({ config }) => {
  const [field, meta] = useField(config);
  return (
    <div>
      <label
        htmlFor="email"
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        {field.name}
      </label>
      <div className="mt-2">
        <input
          {...field}
          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        />
        {meta.error && meta.touched && <div>{meta.error}</div>}
      </div>
    </div>
  );
};
