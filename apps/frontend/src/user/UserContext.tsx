import {
  type PropsWithChildren,
  createContext,
  useCallback,
  useContext,
  useEffect,
} from "react";
import { atom, useAtom, useAtomValue, useSetAtom } from "jotai";

import type { PublicUser } from "@kassierer/shared/auth";
import { UserService } from "@/user/user.service";
import { setGlobalAuthToken } from "@/utils/axios";

type UserContextData = {
  token: string | undefined;
  user: PublicUser | undefined;
};

const { searchParams } = new URL(window.location.href);
/**
 * If this token is set, the user is logged in as a client via an url token
 */
const urlToken = searchParams.get("client_token");
const localToken = localStorage.getItem("access_token");

if (urlToken && !localToken) {
  localStorage.setItem("access_token", urlToken);
}

const authTokenAtom = atom<string>(urlToken ?? localToken ?? undefined);

const userAtom = atom<PublicUser | undefined>(undefined as PublicUser);
const authPendingAtom = atom(true);
const loginErrorAtom = atom(false);

const UserContext = createContext<UserContextData>({
  token: undefined,
  user: undefined,
});

export const UserProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const userToken = useAtomValue(authTokenAtom);
  const [user, setUser] = useAtom(userAtom);
  const setAuthPending = useSetAtom(authPendingAtom);

  // on mount we check for a token,
  // if there is none, the auth is also not pending
  useEffect(() => {
    if (userToken) {
      setGlobalAuthToken(userToken);
    } else {
      setAuthPending(false);
    }
  }, []);

  useEffect(() => {
    if (userToken) {
      UserService.getUser().then((user) => {
        setUser(user);
        setAuthPending(false);
      });
    }
  }, [userToken, setUser]);

  return (
    <UserContext.Provider
      value={{
        token: userToken,
        user: user,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export function useUser() {
  const { user, token } = useContext(UserContext);

  // const token = useMemo(() => token, [token]);
  const [isAuthPending, setAuthPending] = useAtom(authPendingAtom);

  const setUserToken = useSetAtom(authTokenAtom);
  const setLoginError = useSetAtom(loginErrorAtom);

  const login = useCallback(
    async (email: string, password: string) => {
      try {
        setAuthPending(true);
        const response = await UserService.login(email, password);
        setGlobalAuthToken(response.data.access_token);
        localStorage.setItem("access_token", response.data.access_token);
        setUserToken(response.data.access_token);
      } catch {
        setLoginError(true);
      }
    },
    [setUserToken, setLoginError],
  );

  const logout = useCallback(() => {
    setGlobalAuthToken(undefined);
    setUserToken(undefined);
    localStorage.removeItem("access_token");
  }, [setUserToken]);

  return {
    isAuthPending,
    token,
    user,
    isClient: !user?.email,
    //methods
    login,
    logout,
  };
}
