import { useState } from "react";
import { Orders } from "@kassierer/shared/data/orders";
import { useY } from "@/data/useY";
import type { Order } from "@kassierer/shared/model";
import {
  type ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Link } from "react-router-dom";

function AdminOrders() {
  const { doc } = useY();

  const {
    observe: observeOrders,
    getAll: getAllOrders,
    deleteOne: deleteOrder,
  } = Orders(doc);
  const [orders, setOrders] = useState<Order[]>(Array.from(getAllOrders()));
  observeOrders(() => setOrders(Array.from(getAllOrders())));

  const columns: ColumnDef<Order>[] = [
    {
      accessorKey: "id",
      cell: (data) => (
        <Link to={`/admin/orders/${data.getValue()}`}>
          {data.getValue() as string}
        </Link>
      ),
    },
    {
      accessorKey: "userId",
    },
    {
      accessorKey: "tableId",
    },
  ];
  const table = useReactTable({
    columns,
    data: orders,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="pb-32">
      Orders: {orders.length}
      <table className="max-h-screen overflow-scroll">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              <th></th>
              {headerGroup.headers.map((header) => (
                <th key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr key={row.id}>
              <td>
                <button onClick={() => deleteOrder(row.original.id)}>
                  Del
                </button>
              </td>
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default AdminOrders;
