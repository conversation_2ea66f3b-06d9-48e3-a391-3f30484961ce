import { useEffect, useState } from "react";
import { Orders } from "@kassierer/shared/data/orders";
import { useY } from "@/data/useY";
import type { Order, OrderItem } from "@kassierer/shared/model";
import { Products } from "@kassierer/shared/data/products";
import { useParams } from "react-router-dom";

function AdminOrderDetail() {
  const { orderId } = useParams();

  const { doc } = useY();

  const { getOne: getOrder, setOne, observeOne, observe } = Orders(doc);
  const [order, setOrder] = useState<Order>(getOrder(orderId));
  observe(() => setOrder(getOrder(orderId)));
  // observeOne(orderId, () => setOrder(getOrder(orderId)));

  const { getOne: getProduct } = Products(doc);

  const payItem = (productId: string, itemIndex: number) => {
    setOne(orderId, {
      ...order,
      content: order.content.map((item) => {
        if (item.productId === productId) {
          return {
            ...item,
            items: item.items.map<OrderItem>((oItem, i) =>
              i === itemIndex ? { ...oItem, payed: true } : oItem,
            ),
          };
        }
        return item;
      }),
    });
  };

  if (!order) {
    return <div>Loading</div>;
  }

  return (
    <div className="pb-32">
      <h1>Order: {order.createdAt}</h1>
      <div>
        {order.content.map((item) => (
          <div key={item.productId}>
            {getProduct(item.productId).name}: {item.items.length}
            <ul>
              {item.items.map((pItem, i) => (
                <li key={i}>
                  payed:{" "}
                  <button onClick={() => payItem(item.productId, i)}>
                    {pItem.payed ? "true" : "false"}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}

export default AdminOrderDetail;
