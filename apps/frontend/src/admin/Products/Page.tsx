import { Form, Formik } from "formik";
import { AdminProductsPageTable } from "@/admin/Products/Table";
import { TextInput } from "@/ui/Form/TextInput";
import { SelectInput } from "@/ui/Form/SelectInput";
import { Button } from "@/ui/Button/Button";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";

function AdminProductsPage() {
  const products = useProducts((state) => state.products);
  const deleteProduct = useProducts((state) => state.deleteProduct);
  const createProduct = useProducts((state) => state.createProduct);
  const categories = useProductCategories((state) => state.categories);

  return (
    <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <div className="mb-4 font-bold">Neues Produckt</div>
          <Formik
            initialValues={{
              name: "",
              priceCents: 0,
              categoryId: categories?.[0]?.id ?? "",
            }}
            onSubmit={(values, { setSubmitting }) => {
              createProduct(values);
              setSubmitting(false);
            }}
          >
            {({ isSubmitting }) => (
              <Form className="flex gap-4">
                <TextInput config={"name"} />
                <TextInput config={"priceCents"} />
                <SelectInput config={{ name: "categoryId", as: "select" }}>
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </SelectInput>
                <Button size="normal" disabled={isSubmitting}>
                  Erstellen
                </Button>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <div className="mt-12">
        <AdminProductsPageTable
          products={products}
          productCategories={categories}
          onDelete={(product) => {
            deleteProduct(product.id);
          }}
        />
      </div>
    </div>
  );
}

export default AdminProductsPage;
