import { DangerButton } from "@/ui/Button/DangerButton";
import { Table } from "@/ui/Table/Table";
import { TableTd } from "@/ui/Table/TableTd";
import { TableTh } from "@/ui/Table/TableTh";
import { PriceCentUtils } from "@/utils/priceCents";
import type { Product, ProductCategory } from "@kassierer/shared/model";
import {
  type ColumnDef,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { NavLink } from "react-router-dom";

type Props = {
  products: Product[];
  productCategories: ProductCategory[];
  onDelete: (product: Product) => void;
};

const columnHelper = createColumnHelper<Product>();

export const AdminProductsPageTable: React.FC<Props> = ({
  products,
  productCategories,
  onDelete,
}) => {
  const columns: ColumnDef<Product>[] = [
    columnHelper.accessor("name", {
      header: "Name",
    }),
    columnHelper.accessor("priceCents", {
      header: "Preis",
      cell: ({ getValue }) => (
        <span>{PriceCentUtils.toMoneyString(getValue() as number)}</span>
      ),
    }),
    columnHelper.accessor("categoryId", {
      header: "Kategorie",
      cell: ({ getValue }) => {
        const catId = getValue();
        const cat = productCategories.find((pc) => pc.id === catId);
        if (cat) {
          return (
            <NavLink to={`/admin/product-categories/${cat.id}`}>
              {cat.name}
            </NavLink>
          );
        }
        return null;
      },
    }),
  ];

  const table = useReactTable({
    columns,
    data: products,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Table>
      <thead className="bg-gray-50">
        {table.getHeaderGroups().map((headerGroup) => (
          <tr key={headerGroup.id}>
            <TableTh></TableTh>
            {headerGroup.headers.map((header) => (
              <TableTh key={header.id}>
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </TableTh>
            ))}
          </tr>
        ))}
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white">
        {table.getRowModel().rows.map((row) => [
          <tr key={row.id} className="even:bg-gray-50">
            <td>
              <DangerButton size="small" onClick={() => onDelete(row.original)}>
                Löschen
              </DangerButton>
            </td>
            {row.getVisibleCells().map((cell) => (
              <TableTd key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableTd>
            ))}
          </tr>,
        ])}
      </tbody>
    </Table>
  );
};
