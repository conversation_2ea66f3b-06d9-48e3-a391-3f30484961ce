import { DangerButton } from "@/ui/Button/DangerButton";
import { Table } from "@/ui/Table/Table";
import { TableTd } from "@/ui/Table/TableTd";
import { TableTh } from "@/ui/Table/TableTh";
import type {
  Order,
  Product,
  Table as TableModel,
} from "@kassierer/shared/model";
import {
  type ColumnDef,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

type Props = {
  orders: Order[];
  tables: TableModel[];
  productsMap: Record<string, Product>;
  onDelete: (table: Order) => void;
};

const columnHelper = createColumnHelper<Order>();

export const AdminOrderPageTable: React.FC<Props> = ({
  orders,
  tables,
  productsMap,
  onDelete,
}) => {
  const columns: ColumnDef<Order>[] = [
    columnHelper.accessor("tableId", {
      header: "Tisch",
      cell: ({ cell }) => {
        const orderTable = tables.find((t) => t.id === cell.getValue());
        if (orderTable) {
          return <span>{orderTable.name}</span>;
        }
        return <span>-</span>;
      },
    }),
    columnHelper.accessor("content", {
      header: "Inhalt",
      cell: ({ cell }) => {
        const content = cell.getValue();
        return (
          <ul>
            {content.map((orderItem) => (
              <li>
                {productsMap[orderItem.productId]?.name}:{" "}
                {orderItem.items.length}
              </li>
            ))}
          </ul>
        );
      },
    }),
  ];

  const table = useReactTable({
    columns,
    data: orders,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Table>
      <thead className="bg-gray-50">
        {table.getHeaderGroups().map((headerGroup) => (
          <tr key={headerGroup.id}>
            <TableTh></TableTh>
            {headerGroup.headers.map((header) => (
              <TableTh key={header.id}>
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </TableTh>
            ))}
          </tr>
        ))}
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white">
        {table.getRowModel().rows.map((row) => [
          <tr key={row.id} className="even:bg-gray-50">
            <td>
              <DangerButton size="small" onClick={() => onDelete(row.original)}>
                Löschen
              </DangerButton>
            </td>
            {row.getVisibleCells().map((cell) => (
              <TableTd key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableTd>
            ))}
          </tr>,
        ])}
      </tbody>
    </Table>
  );
};
