import { useRef } from "react";
import { AdminProductCategoryTable } from "@/admin/Categories/Table";
import { useProductCategories } from "@/data/useProductCategories";

export function AdminProductCategories() {
  const categories = useProductCategories((state) => state.categories);
  const deleteCategory = useProductCategories(
    (state) => state.deleteCategory,
  );
  const createCategory = useProductCategories(
    (state) => state.createCategory,
  );

  const inputRef = useRef(null);

  return (
    <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <div className="mb-4 font-bold">Neue Produkt Kategorie</div>
          <input ref={inputRef} type="text" />
          <button
            onClick={() => createCategory({ name: inputRef.current.value })}
          >
            Add
          </button>
        </div>
      </div>

      <div className="mt-12">
        <AdminProductCategoryTable
          categories={categories}
          onDelete={(cat) => deleteCategory(cat.id)}
        />
      </div>
    </div>
  );
}
