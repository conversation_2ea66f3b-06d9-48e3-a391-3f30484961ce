import { Form, Formik } from "formik";
import { TextInput } from "@/ui/Form/TextInput";
import { Button } from "@/ui/Button/Button";
import { AdminTablesPageTable } from "@/admin/Tables/Table";
import { useTables } from "@/data/useTables";

function AdminTablesPage() {
  const tables = useTables((state) => state.tables);
  const deleteTable = useTables((state) => state.deleteTable);
  const createTable = useTables((state) => state.createTable);

  return (
    <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div className="overflow-hidden rounded-lg bg-white shadow">
        <div className="px-4 py-5 sm:p-6">
          <div className="mb-4 font-bold">Neuer Tisch</div>
          <Formik
            initialValues={{
              name: "",
            }}
            onSubmit={(values, { setSubmitting }) => {
              createTable(values);
              setSubmitting(false);
            }}
          >
            {({ isSubmitting }) => (
              <Form className="flex gap-4">
                <TextInput config={"name"} />
                <Button size="normal" disabled={isSubmitting}>
                  Erstellen
                </Button>
              </Form>
            )}
          </Formik>
        </div>
      </div>

      <div className="mt-12">
        <AdminTablesPageTable
          tables={tables}
          onDelete={(table) => {
            deleteTable(table.id);
          }}
        />
      </div>
    </div>
  );
}

export default AdminTablesPage;
