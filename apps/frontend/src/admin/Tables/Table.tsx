import { DangerButton } from "@/ui/Button/DangerButton";
import { Table } from "@/ui/Table/Table";
import { TableTd } from "@/ui/Table/TableTd";
import { TableTh } from "@/ui/Table/TableTh";
import { type Table as TableModel } from "@kassierer/shared/model";
import {
  type ColumnDef,
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";

type Props = {
  tables: TableModel[];
  onDelete: (table: TableModel) => void;
};

const columnHelper = createColumnHelper<TableModel>();

export const AdminTablesPageTable: React.FC<Props> = ({ tables, onDelete }) => {
  const columns: ColumnDef<TableModel>[] = [
    columnHelper.accessor("name", {
      header: "Name",
    }),
  ];

  const table = useReactTable({
    columns,
    data: tables,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <Table>
      <thead className="bg-gray-50">
        {table.getHeaderGroups().map((headerGroup) => (
          <tr key={headerGroup.id}>
            <TableTh></TableTh>
            {headerGroup.headers.map((header) => (
              <TableTh key={header.id}>
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </TableTh>
            ))}
          </tr>
        ))}
      </thead>
      <tbody className="divide-y divide-gray-200 bg-white">
        {table.getRowModel().rows.map((row) => [
          <tr key={row.id} className="even:bg-gray-50">
            <td>
              <DangerButton size="small" onClick={() => onDelete(row.original)}>
                Löschen
              </DangerButton>
            </td>
            {row.getVisibleCells().map((cell) => (
              <TableTd key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </TableTd>
            ))}
          </tr>,
        ])}
      </tbody>
    </Table>
  );
};
