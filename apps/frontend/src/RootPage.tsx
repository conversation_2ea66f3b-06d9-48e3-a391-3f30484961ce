import { useUser } from "@/user/UserContext";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function RootPage() {
  const navigate = useNavigate();
  const { token } = useUser();

  useEffect(() => {
    if (!token) {
      navigate("/login");
    } else {
      navigate("/admin/products");
    }
  }, [token, navigate]);

  return <div>Root</div>;
}
