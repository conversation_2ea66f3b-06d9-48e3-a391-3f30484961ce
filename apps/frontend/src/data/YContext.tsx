import { YService } from "@/data/YService";
import { useUser } from "@/user/UserContext";
import {
  createContext,
  type PropsWithChildren,
  useEffect,
  useRef,
  useState,
} from "react";
import * as Y from "yjs";

type YContextState = {
  doc: Y.Doc | null;
  synced: boolean;
  connected: boolean;
};

export const YContext = createContext<YContextState>({
  doc: null,
  synced: false,
  connected: false,
});

export const YProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [yDocSynced, setIsSynced] = useState(false);
  const [yDocConnected, setIsConnected] = useState(false);
  const yDoc = useRef(new Y.Doc());

  useEffect(() => {
    YService.setupDocAndConnections(yDoc.current);
    YService.onSynced(() => {
      setIsSynced(true);
    });
    YService.onConnected(() => {
      setIsConnected(true);
    });
    YService.onDisconnected(() => {
      setIsConnected(false);
    });
  }, []);

  const { token } = useUser();

  useEffect(() => {
    if (token) {
      const newDoc = new Y.Doc();
      yDoc.current = newDoc;
      YService.setupDocAndConnections(newDoc, token ? "" : undefined, token);

      return () => {
        setIsSynced(false);
        setIsConnected(false);
        yDoc.current.destroy();
      };
    }
  }, [token]);

  return (
    <YContext.Provider
      value={{
        doc: yDoc.current,
        synced: yDocSynced,
        connected: yDocConnected,
      }}
    >
      {children}
    </YContext.Provider>
  );
};
