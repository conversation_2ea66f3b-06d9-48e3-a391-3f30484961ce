import { useY } from "@/data/useY";
import { Tables } from "@kassierer/shared/data/table";
import type { Table } from "@kassierer/shared/model";
import { create } from "zustand";

type TableStore = {
  tables: Table[];
  createTable: (table: Omit<Table, "id">) => void;
  deleteTable: (id: Table["id"]) => void;
  setTable: (id: Table["id"], table: Table) => void;
};

export const useTables = create<TableStore>((set) => {
  const { doc } = useY();
  const { observe, getAll, createOne, deleteOne, setOne } = Tables(doc);
  observe(() => set({ tables: Array.from(getAll()) }));

  return {
    tables: Array.from(getAll()),
    createTable: createOne,
    deleteTable: deleteOne,
    setTable: setOne,
  };
})