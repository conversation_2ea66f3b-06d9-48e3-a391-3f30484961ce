import { useY } from "@/data/useY";
import { Products } from "@kassierer/shared/data/products";
import type { Product } from "@kassierer/shared/model";
import { create } from "zustand";

type ProductStore = {
  products: Product[];
  productsMap: Record<Product["id"], Product>;
  deleteProduct: (id: Product["id"]) => void;
  createProduct: (product: Omit<Product, "id">) => void;
  setProduct: (id: Product["id"], product: Product) => void;
};

export const useProducts = create<ProductStore>((set, get) => {
  const { doc } = useY();
  const { observe, getAll, deleteOne, createOne, setOne } = Products(doc);
  observe(() => set({ products: Array.from(getAll()) }));

  return {
    products: Array.from(getAll()),
    get productsMap() {
      return get().products.reduce<Record<Product["id"], Product>>(
        (pMap, product) => {
          pMap[product.id] = product;
          return pMap;
        },
        {},
      );
    },
    deleteProduct: deleteOne,
    createProduct: createOne,
    setProduct: setOne,
  };
});
