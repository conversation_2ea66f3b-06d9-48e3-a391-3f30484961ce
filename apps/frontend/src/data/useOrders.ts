import { useY } from "@/data/useY";
import { Orders } from "@kassierer/shared/data/orders";
import type { Order } from "@kassierer/shared/model";
import { create } from "zustand";

type OrderStore = {
  orders: Order[];
  deleteOrder: (id: Order["id"]) => void;
  createOrder: (order: Omit<Order, "id" | "createdAt">) => void;
  updateOrder: (id: Order["id"], order: Order) => void;
};

export const useOrders = create<OrderStore>((set) => {
  const { doc } = useY();
  const { observe, getAll, deleteOne, createOne, setOne } = Orders(doc);

  // updates from yjs
  observe(() => {
    const newOrders = Array.from(getAll())
    set({ orders: newOrders });
  });

  return {
    orders: Array.from(getAll()),
    deleteOrder: deleteOne,
    createOrder: createOne,
    updateOrder: setOne,
  }
})