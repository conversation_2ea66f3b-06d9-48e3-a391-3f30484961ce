import { useY } from "@/data/useY";
import { ProductCategories } from "@kassierer/shared/data/product-category";
import type { ProductCategory } from "@kassierer/shared/model";
import { create } from "zustand";

type ProductCategoryStore = {
  categories: ProductCategory[];
  deleteCategory: (id: ProductCategory["id"]) => void;
  createCategory: (category: Omit<ProductCategory, "id">) => void;
  updateCategory: (id: ProductCategory["id"], category: ProductCategory) => void;
};

export const useProductCategories = create<ProductCategoryStore>((set) => {
  const { doc } = useY();
  const { observe, getAll, deleteOne, createOne, setOne } = ProductCategories(doc);
  observe(() => set({ categories: Array.from(getAll()) }));

  return {
    categories: Array.from(getAll()),
    deleteCategory: deleteOne,
    createCategory: createOne,
    updateCategory: setOne
  };
})